"use client";

import { useEffect } from "react";
import { useDashboardHeader } from "./context";
import { HeaderConfig } from "./types";

/**
 * Hook to set the header configuration
 * Useful for custom pages with unique headers
 */
export const useSetHeaderConfig = (config: HeaderConfig) => {
  const { setConfig } = useDashboardHeader();

  useEffect(() => {
    setConfig(config);
  }, [config, setConfig]);
};

/**
 * Hook to update specific parts of the header configuration
 * Useful for dynamic updates based on component state
 */
export const useUpdateHeaderConfig = () => {
  const { updateConfig } = useDashboardHeader();
  return updateConfig;
};

/**
 * Hook to get the current header configuration
 * Useful for reading the current state
 */
export const useHeaderConfig = () => {
  const { config } = useDashboardHeader();
  return config;
};

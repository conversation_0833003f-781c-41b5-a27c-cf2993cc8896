"use client";

import React, { createContext, useContext, useState, useCallback } from "react";
import { 
  DashboardHeaderContextValue, 
  DashboardHeaderProviderProps, 
  HeaderConfig 
} from "./types";

const defaultConfig: HeaderConfig = {
  title: "Dashboard",
  description: "Welcome to your dashboard",
  actions: []
};

// Create the context
const DashboardHeaderContext = createContext<DashboardHeaderContextValue | null>(null);

// Provider component
export const DashboardHeaderProvider: React.FC<DashboardHeaderProviderProps> = ({ 
  children 
}) => {
  const [config, setConfigState] = useState<HeaderConfig>(defaultConfig);

  const setConfig = useCallback((newConfig: HeaderConfig) => {
    setConfigState(newConfig);
  }, []);

  const updateConfig = useCallback((updates: Partial<HeaderConfig>) => {
    setConfigState(prev => ({
      ...prev,
      ...updates,
      actions: updates.actions !== undefined ? updates.actions : prev.actions
    }));
  }, []);

  const value: DashboardHeaderContextValue = {
    config,
    setConfig,
    updateConfig
  };

  return (
    <DashboardHeaderContext.Provider value={value}>
      {children}
    </DashboardHeaderContext.Provider>
  );
};

// Custom hook to use the context
export const useDashboardHeader = (): DashboardHeaderContextValue => {
  const context = useContext(DashboardHeaderContext);
  
  if (!context) {
    throw new Error(
      "useDashboardHeader must be used within a DashboardHeaderProvider"
    );
  }
  
  return context;
};

// Export context for advanced usage
export { DashboardHeaderContext };

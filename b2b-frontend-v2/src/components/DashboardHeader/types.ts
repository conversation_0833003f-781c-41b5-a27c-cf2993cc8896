import { ReactNode } from "react";
import { ButtonVariant } from "../Button/types";

export interface HeaderAction {
  label: string;
  variant: ButtonVariant;
  onClick: () => void;
  className?: string;
  disabled?: boolean;
}

export interface HeaderConfig {
  title: string;
  description?: string;
  actions?: HeaderAction[];
}

export interface DashboardHeaderContextValue {
  config: HeaderConfig;
  setConfig: (config: HeaderConfig) => void;
  updateConfig: (updates: Partial<HeaderConfig>) => void;
}

export interface DashboardHeaderProviderProps {
  children: ReactNode;
}

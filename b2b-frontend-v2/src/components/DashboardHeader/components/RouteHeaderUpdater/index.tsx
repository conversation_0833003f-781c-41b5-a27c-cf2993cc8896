"use client";

import { useEffect } from "react";
import { usePathname } from "next/navigation";
import { useDashboardHeader } from "../../context";
import { getHeaderConfigByRoute } from "@/constants/header/configs";

/**
 * Component that automatically updates the dashboard header based on the current route
 * This should be placed inside the DashboardHeaderProvider
 */
const RouteHeaderUpdater = () => {
  const pathname = usePathname();
  const { setConfig } = useDashboardHeader();

  useEffect(() => {
    const config = getHeaderConfigByRoute(pathname);
    setConfig(config);
  }, [pathname]);

  // This component doesn't render anything
  return null;
};

export default RouteHeaderUpdater;

import React from "react";
import Button from "../Button";
import { useDashboardHeader } from "./context";

const DashboardHeader = () => {
  const { config } = useDashboardHeader();

  return (
    <div className="w-full bg-[#FAFAFA] border-b border-[#D4D4D4] px-6 py-2">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-lg font-semibold text-neutral-900 mb-1">
            {config.title}
          </h1>
          {config.description && (
            <p className="text-xs text-neutral-600">
              {config.description}
            </p>
          )}
        </div>
        {config.actions && config.actions.length > 0 && (
          <div className="flex items-center gap-3">
            {config.actions.map((action, index) => (
              <Button
                key={index}
                variant={action.variant}
                className={action.className}
                onClick={action.onClick}
                disabled={action.disabled}
              >
                {action.label}
              </Button>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default DashboardHeader;

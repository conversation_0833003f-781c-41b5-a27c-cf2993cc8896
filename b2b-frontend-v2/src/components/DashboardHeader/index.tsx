import React from "react";
import Button from "../But<PERSON>";

const DashboardHeader = () => {
  return (
    <div className="w-full bg-[#FAFAFA] border-b border-[#D4D4D4] px-6 py-2">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-lg font-semibold text-neutral-900 mb-1">
            Document Library
          </h1>
          <p className="text-xs text-neutral-600">
            Select a folder to view the documents in that
          </p>
        </div>
        <div className="flex items-center gap-3">
          <Button
            variant="ghost"
            className="!px-4 !py-2 !text-sm !font-medium !border-neutral-300 !text-neutral-700 !bg-white hover:!bg-neutral-50 !w-auto"
          >
            Edit Document
          </Button>
          <Button
            variant="primary"
            className="!px-4 !py-2 !text-sm !font-medium !bg-green-600 hover:!bg-green-700 !text-white !w-auto"
          >
            New Document
          </Button>
        </div>
      </div>
    </div>
  );
};

export default DashboardHeader;

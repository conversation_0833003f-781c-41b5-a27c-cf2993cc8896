"use client";

import { useState } from "react";
import { sidebarItems } from "@/constants/common/data";
import Sidebar from "@/components/Sidebar";
import DashboardHeader from "../DashboardHeader";
import { DashboardLayoutPropsI } from "./types";

const DashboardLayout = ({ children }: DashboardLayoutPropsI) => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(true);
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());

  const handleToggleSidebar = () => {
    setSidebarCollapsed((prev) => {
      const newCollapsed = !prev;
      if (newCollapsed) {
        setExpandedItems(new Set());
      }
      return newCollapsed;
    });
  };

  const handleToggleExpand = (itemHref: string) => {
    setExpandedItems((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(itemHref)) {
        newSet.delete(itemHref);
      } else {
        newSet.add(itemHref);
      }
      return newSet;
    });
  };

  return (
    <main className="flex min-h-screen max-h-screen w-full overflow-hidden">
      <Sidebar
        items={sidebarItems}
        hasLogo
        level={1}
        collapsed={sidebarCollapsed}
        className="flex-shrink-0"
        onToggleSidebar={handleToggleSidebar}
        onToggleExpand={handleToggleExpand}
        expandedItems={expandedItems}
      />
      <div className="flex-1 flex flex-col overflow-hidden">
        <DashboardHeader
          title="Document Library"
          description="Select a folder to view the documents in that"
          actions={[
            {
              label: "Edit Document",
              variant: "ghost",
              className: "!px-4 !py-2 !text-sm !font-medium !border-neutral-300 !text-neutral-700 hover:!bg-neutral-50 !w-auto",
              onClick: () => console.log("Edit Document clicked")
            },
            {
              label: "New Document",
              variant: "primary",
              className: "!px-4 !py-2 !text-sm !font-medium !text-white !w-auto",
              onClick: () => console.log("New Document clicked")
            }
          ]}
        />
        <div className="flex-1 overflow-auto">{children}</div>
      </div>
    </main>
  );
};
export default DashboardLayout;

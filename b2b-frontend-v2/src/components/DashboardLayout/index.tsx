"use client";

import { useState } from "react";
import { sidebarItems } from "@/constants/common/data";
import Sidebar from "@/components/Sidebar";
import DashboardHeader from "../DashboardHeader";
import { DashboardHeaderProvider } from "../DashboardHeader/context";
import RouteHeaderUpdater from "../DashboardHeader/components/RouteHeaderUpdater";
import { DashboardLayoutPropsI } from "./types";

const DashboardLayout = ({ children }: DashboardLayoutPropsI) => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(true);
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());

  const handleToggleSidebar = () => {
    setSidebarCollapsed((prev) => {
      const newCollapsed = !prev;
      // When collapsing sidebar, collapse all expanded items
      if (newCollapsed) {
        setExpandedItems(new Set());
      }
      return newCollapsed;
    });
  };

  const handleToggleExpand = (itemHref: string) => {
    setExpandedItems((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(itemHref)) {
        newSet.delete(itemHref);
      } else {
        newSet.add(itemHref);
      }
      return newSet;
    });
  };

  return (
    <DashboardHeaderProvider>
      <RouteHeaderUpdater />
      <main className="flex min-h-screen max-h-screen w-full overflow-hidden">
        <Sidebar
          items={sidebarItems}
          hasLogo
          level={1}
          collapsed={sidebarCollapsed}
          className="flex-shrink-0"
          onToggleSidebar={handleToggleSidebar}
          onToggleExpand={handleToggleExpand}
          expandedItems={expandedItems}
        />
        <div className="flex-1 flex flex-col">
          <DashboardHeader />
          <div className="flex-1 overflow-auto">{children}</div>
        </div>
      </main>
    </DashboardHeaderProvider>
  );
};
export default DashboardLayout;

import { ClassNameI, LanguageSupported } from "@/types/common/data";
import { ReactNode } from "react";

export interface SidebarSubItem {
  label: string;
  href: string;
}

export interface SidebarItem {
  icon: ReactNode;
  iconActive: ReactNode;
  label: string;
  href: string;
  isExpanded?: boolean;
  isBottom?: boolean;
  isCollapsible?: boolean;
  subItems?: SidebarSubItem[];
  isToggle?: boolean;
}

export interface SidebarProps {
  items: SidebarItem[];
  hasLogo?: boolean;
  title?: string;
  level: number;
  className?: ClassNameI;
  collapsed?: boolean;
  onToggleExpand?: (itemHref: string) => void;
  expandedItems?: Set<string>;
  onToggleSidebar?: () => void;
}

export interface SidebarItemProps extends SidebarItem {
  direction: "rtl" | "ltr";
  locale: LanguageSupported;
  level: number;
  collapsed?: boolean;
  onToggleExpand?: (itemHref: string) => void;
  expandedItems?: Set<string>;
  onToggleSidebar?: () => void;
}

"use client";

import { usePathname } from "next/navigation";
import { useLocale, useTranslations } from "next-intl";
import { getLangDir } from "rtl-detect";
import { cn } from "@/utils/class-merge";
import { SidebarProps, SidebarItemProps } from "./types";
import { LanguageSupported } from "@/types/common/data";
import Logo from "../Logo";
import React, { useState } from "react";
import Link from "next/link";
import Tooltip from "../Tooltip";
import { ChevronDownIcon } from "@/assets/images/svgs/common";

const SidebarItem: React.FC<SidebarItemProps> = ({
  icon,
  iconActive,
  label,
  href,
  level,
  locale,
  direction,
  collapsed,
  subItems,
  onToggleExpand,
  expandedItems,
  isToggle,
  onToggleSidebar,
}) => {
  const pathname = usePathname();
  const t = useTranslations();

  const pathWithoutLocale = pathname.replace(`/${locale}`, "");
  const targetPath = href.startsWith("/") ? href : `/${href}`;

  const isActive = (() => {
    const currentSegment = pathWithoutLocale.split("/")[1];
    const targetSegment = targetPath.split("/")[1];

    if (level === 1) {
      return currentSegment === targetSegment;
    }
    return pathWithoutLocale === targetPath;
  })();

  const hasSubItems = subItems && subItems.length > 0;
  const isExpanded = expandedItems?.has(href) || false;

  const handleToggleExpand = (e: React.MouseEvent | React.KeyboardEvent) => {
    if (hasSubItems && onToggleExpand) {
      e.preventDefault();
      onToggleExpand(href);
    }
  };

  const handleToggleSidebar = (e: React.MouseEvent | React.KeyboardEvent) => {
    if (isToggle && onToggleSidebar) {
      e.preventDefault();
      onToggleSidebar();
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (isToggle) {
      switch (e.key) {
        case "Enter":
        case " ":
          handleToggleSidebar(e);
          break;
      }
    } else if (hasSubItems) {
      switch (e.key) {
        case "Enter":
        case " ":
          handleToggleExpand(e);
          break;
        case "ArrowDown":
          if (isExpanded) {
            e.preventDefault();
            // Focus first sub-item
            const firstSubItem = document.querySelector(`#submenu-${href} a`);
            if (firstSubItem) {
              (firstSubItem as HTMLElement).focus();
            }
          }
          break;
        case "ArrowUp":
          if (isExpanded) {
            e.preventDefault();
            // Focus last sub-item
            const subItems = document.querySelectorAll(`#submenu-${href} a`);
            if (subItems.length > 0) {
              (subItems[subItems.length - 1] as HTMLElement).focus();
            }
          }
          break;
        case "Escape":
          if (isExpanded) {
            e.preventDefault();
            handleToggleExpand(e);
          }
          break;
      }
    }
  };

  // Toggle button for sidebar collapse/expand
  if (isToggle) {
    return (
      <button
        onClick={handleToggleSidebar}
        onKeyDown={handleKeyDown}
        className={cn(
          "flex items-center w-full transition-all overflow-hidden text-ellipsis duration-300 ease-in-out",
          collapsed ? "p-2 justify-center" : "px-3 py-2",
          !collapsed &&
            (direction === "rtl" ? "rounded-l-full" : "rounded-r-full"),
          collapsed && "rounded-full",
          "text-neutral-700 hover:bg-neutral-100 hover:text-neutral-900",
          "focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2",
          "group",
        )}
        aria-label={collapsed ? "Expand sidebar" : "Collapse sidebar"}
        role="button"
        tabIndex={0}
      >
        {collapsed ? (
          <Tooltip
            content={collapsed ? "Expand sidebar" : "Collapse sidebar"}
            position={direction === "rtl" ? "left" : "right"}
          >
            <div
              className={cn(
                "flex-shrink-0 transition-all duration-300 ease-in-out fill-neutral-700 group-hover:fill-neutral-900",
                collapsed ? "transform rotate-0" : "transform rotate-180",
                direction === "rtl" && "scale-x-[-1]",
              )}
            >
              {icon}
            </div>
          </Tooltip>
        ) : (
          <div
            className={cn(
              "flex items-center gap-3 w-full",
              direction === "rtl" && "flex-row-reverse",
            )}
          >
            <div
              className={cn(
                "flex-shrink-0 transition-all duration-300 ease-in-out fill-neutral-700 group-hover:fill-neutral-900",
                collapsed ? "transform rotate-0" : "transform rotate-180",
                direction === "rtl" && "scale-x-[-1]",
              )}
            >
              {icon}
            </div>
            <span className="text-sm whitespace-nowrap transition-all duration-300 font-normal group-hover:font-medium">
              {t(label)}
            </span>
          </div>
        )}
        <span className="sr-only">
          {collapsed ? "Expand sidebar" : "Collapse sidebar"}
        </span>
      </button>
    );
  }

  if (hasSubItems && !collapsed) {
    // Expandable item with sub-items
    return (
      <div>
        <button
          id={`button-${href}`}
          onClick={handleToggleExpand}
          onKeyDown={handleKeyDown}
          className={cn(
            "flex items-center w-full transition-all overflow-hidden text-ellipsis duration-300 ease-in-out",
            "px-3 py-2",
            direction === "rtl" ? "rounded-l-full" : "rounded-r-full",
            isActive
              ? "bg-secondary-100 text-primary-600 font-medium transform scale-105"
              : "text-neutral-700 hover:bg-neutral-100 hover:text-neutral-900 hover:transform hover:scale-102",
            "focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2",
            "group",
          )}
          aria-expanded={isExpanded}
          aria-controls={`submenu-${href}`}
          aria-label={`${t(label)} - ${isExpanded ? "Collapse" : "Expand"} submenu`}
          role="button"
          tabIndex={0}
        >
          <div
            className={cn(
              "flex items-center gap-3 w-full",
              direction === "rtl" && "flex-row-reverse",
            )}
          >
            <div
              className={cn(
                "flex-shrink-0 transition-all duration-300 ease-in-out group-hover:scale-110",
                isActive
                  ? "fill-primary-600"
                  : "fill-neutral-700 group-hover:fill-neutral-900",
              )}
            >
              {isActive ? iconActive : icon}
            </div>
            <span
              className={cn(
                "text-sm whitespace-nowrap transition-all duration-300 ease-in-out flex-1",
                isActive
                  ? "font-medium"
                  : "font-normal group-hover:font-medium",
                direction === "rtl" ? "text-right" : "text-left",
              )}
            >
              {t(label)}
            </span>
            <div
              className={cn(
                "flex-shrink-0 transition-all duration-300 ease-in-out group-hover:scale-110",
                isExpanded && "rotate-180",
                isActive
                  ? "text-primary-600"
                  : "text-neutral-700 group-hover:text-neutral-900",
              )}
            >
              <ChevronDownIcon />
            </div>
          </div>
          <span className="sr-only">
            {isExpanded ? "Collapse" : "Expand"} {t(label)}
          </span>
        </button>

        {/* Sub-items */}
        <div
          id={`submenu-${href}`}
          className={cn(
            "overflow-hidden transition-all duration-300 ease-in-out",
            isExpanded
              ? "max-h-96 opacity-100 transform translate-y-0"
              : "max-h-0 opacity-0 transform -translate-y-2",
          )}
          role="menu"
          aria-labelledby={`button-${href}`}
        >
          <div className="py-1 transition-all duration-300 ease-in-out">
            {subItems?.map((subItem, index) => (
              <Link
                key={index}
                href={subItem.href}
                className={cn(
                  "flex items-center w-full transition-all duration-300 ease-in-out",
                  "px-3 py-2 ml-6",
                  direction === "rtl"
                    ? "rounded-l-full mr-6 ml-0"
                    : "rounded-r-full",
                  pathWithoutLocale === subItem.href
                    ? "bg-secondary-50 text-primary-600 font-medium transform scale-105"
                    : "text-neutral-600 hover:bg-neutral-50 hover:text-neutral-700 hover:transform hover:scale-102",
                  "focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2",
                  "group",
                )}
                role="menuitem"
                tabIndex={isExpanded ? 0 : -1}
                onKeyDown={(e) => {
                  switch (e.key) {
                    case "ArrowDown":
                      e.preventDefault();
                      const nextSibling =
                        e.currentTarget.parentElement?.nextElementSibling?.querySelector(
                          "a",
                        );
                      if (nextSibling) {
                        (nextSibling as HTMLElement).focus();
                      }
                      break;
                    case "ArrowUp":
                      e.preventDefault();
                      const prevSibling =
                        e.currentTarget.parentElement?.previousElementSibling?.querySelector(
                          "a",
                        );
                      if (prevSibling) {
                        (prevSibling as HTMLElement).focus();
                      } else {
                        // Focus back to parent button
                        const parentButton = document.querySelector(
                          `button[aria-controls="submenu-${href}"]`,
                        );
                        if (parentButton) {
                          (parentButton as HTMLElement).focus();
                        }
                      }
                      break;
                    case "Escape":
                      e.preventDefault();
                      const parentBtn = document.querySelector(
                        `button[aria-controls="submenu-${href}"]`,
                      );
                      if (parentBtn) {
                        (parentBtn as HTMLElement).focus();
                      }
                      break;
                  }
                }}
              >
                <span className="text-sm whitespace-nowrap transition-all duration-300 ease-in-out group-hover:font-medium">
                  {t(subItem.label)}
                </span>
              </Link>
            ))}
          </div>
        </div>
      </div>
    );
  }

  // Regular item (non-expandable or collapsed sidebar)
  return (
    <Link
      href={href}
      className={cn(
        "flex items-center w-full transition-all overflow-hidden text-ellipsis duration-300 ease-in-out",
        collapsed ? "p-2 justify-center" : "px-3 py-2",
        !collapsed &&
          (direction === "rtl" ? "rounded-l-full" : "rounded-r-full"),
        collapsed && "rounded-full",
        isActive
          ? "bg-secondary-100 text-primary-600 font-medium transform scale-105"
          : "text-neutral-700 hover:bg-neutral-100 hover:text-neutral-900 hover:transform hover:scale-102",
        "focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2",
        "group",
      )}
      aria-current={isActive ? "page" : undefined}
    >
      {collapsed ? (
        <Tooltip
          content={t(label)}
          position={direction === "rtl" ? "left" : "right"}
        >
          <div
            className={cn(
              "flex-shrink-0 transition-all duration-300 ease-in-out group-hover:scale-110",
              isActive
                ? "fill-primary-600"
                : "fill-neutral-700 group-hover:fill-neutral-900",
            )}
          >
            {isActive ? iconActive : icon}
          </div>
        </Tooltip>
      ) : (
        <div
          className={cn(
            "flex items-center gap-3 w-full",
            direction === "rtl" && "flex-row-reverse",
          )}
        >
          <div
            className={cn(
              "flex-shrink-0 transition-all duration-300 ease-in-out group-hover:scale-110",
              isActive
                ? "fill-primary-600"
                : "fill-neutral-700 group-hover:fill-neutral-900",
            )}
          >
            {isActive ? iconActive : icon}
          </div>
          <span
            className={cn(
              "text-sm whitespace-nowrap transition-all duration-300 ease-in-out",
              isActive ? "font-medium" : "font-normal group-hover:font-medium",
            )}
          >
            {t(label)}
          </span>
        </div>
      )}
      <span className="sr-only">{t(label)}</span>
    </Link>
  );
};

const Sidebar: React.FC<SidebarProps> = ({
  items,
  hasLogo,
  title,
  className,
  level,
  collapsed = false,
  onToggleExpand,
  expandedItems,
  onToggleSidebar,
}) => {
  const locale = useLocale() as LanguageSupported;
  const direction = getLangDir(locale);
  const [internalExpandedItems, setInternalExpandedItems] = useState<
    Set<string>
  >(new Set());

  // Use external state if provided, otherwise use internal state
  const currentExpandedItems = expandedItems || internalExpandedItems;

  const handleToggleExpand = (itemHref: string) => {
    if (onToggleExpand) {
      onToggleExpand(itemHref);
    } else {
      // Use internal state management
      setInternalExpandedItems((prev) => {
        const newSet = new Set(prev);
        if (newSet.has(itemHref)) {
          newSet.delete(itemHref);
        } else {
          newSet.add(itemHref);
        }
        return newSet;
      });
    }
  };

  const regularItems = items.filter((item) => !item.isBottom);
  const bottomItems = items.filter((item) => item.isBottom);

  return (
    <aside
      className={cn(
        "flex flex-col h-screen z-[60] bg-white transition-all duration-300 ease-in-out",
        collapsed ? "w-16 p-3" : "w-64 pt-3 pb-3",
        !collapsed && (direction === "rtl" ? "pl-3" : "pr-3"),
        direction === "rtl"
          ? "border-l border-neutral-200"
          : "border-r border-neutral-200",
        className,
      )}
    >
      {hasLogo && (
        <div
          className={cn(
            "flex justify-center transition-all duration-300 ease-in-out",
            !collapsed && "pl-3",
          )}
        >
          <Logo
            compact={collapsed}
            imageSize={32}
            className="w-8 h-8 transition-all duration-300 ease-in-out"
          />
        </div>
      )}
      {!collapsed && title && (
        <span
          className={cn(
            "text-base font-medium text-neutral-950 transition-all duration-300 ease-in-out",
            direction === "rtl" ? "pr-3" : "pl-3",
          )}
        >
          {title}
        </span>
      )}
      {level === 1 && (
        <div className="mx-2 my-4 border-t border-neutral-200"></div>
      )}
      <nav className="space-y-3 mt-4 transition-all duration-300 ease-in-out">
        {regularItems.map((item, index) => (
          <SidebarItem
            key={index}
            {...item}
            level={level}
            direction={direction}
            locale={locale}
            collapsed={collapsed}
            onToggleExpand={handleToggleExpand}
            expandedItems={currentExpandedItems}
            onToggleSidebar={onToggleSidebar}
          />
        ))}
      </nav>
      <nav className="mt-auto space-y-3 transition-all duration-300 ease-in-out">
        {bottomItems.map((item, index) => (
          <SidebarItem
            key={index}
            {...item}
            level={level}
            direction={direction}
            locale={locale}
            collapsed={collapsed}
            onToggleExpand={handleToggleExpand}
            expandedItems={currentExpandedItems}
            onToggleSidebar={onToggleSidebar}
          />
        ))}
      </nav>
    </aside>
  );
};

export default Sidebar;

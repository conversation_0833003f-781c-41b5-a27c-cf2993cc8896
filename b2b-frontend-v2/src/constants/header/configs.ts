import { HeaderConfig } from "@/components/DashboardHeader/types";

export const headerConfigs: Record<string, HeaderConfig> = {
  library: {
    title: "Document Library",
    description: "Select a folder to view the documents in that",
    actions: [
      {
        label: "Edit Document",
        variant: "ghost" as const,
        onClick: () => console.log("Edit Document clicked"),
        className: "!px-4 !py-2 !text-sm !font-medium !border-neutral-300 !text-neutral-700 hover:!bg-neutral-50 !w-auto"
      },
      {
        label: "New Document",
        variant: "primary" as const,
        onClick: () => console.log("New Document clicked"),
        className: "!px-4 !py-2 !text-sm !font-medium !text-white !w-auto"
      }
    ]
  },

  libraryDocuments: {
    title: "Document Library",
    description: "Select a folder to view the documents in that",
    actions: [
      {
        label: "Edit Document",
        variant: "ghost" as const,
        onClick: () => console.log("Edit Document clicked"),
        className: "!px-4 !py-2 !text-sm !font-medium !border-neutral-300 !text-neutral-700 hover:!bg-neutral-50 !w-auto"
      },
      {
        label: "New Document",
        variant: "primary" as const,
        onClick: () => console.log("New Document clicked"),
        className: "!px-4 !py-2 !text-sm !font-medium !text-white !w-auto"
      }
    ]
  },

  libraryDrafts: {
    title: "My Drafts",
    description: "View and edit your draft documents",
    actions: [
      {
        label: "New Draft",
        variant: "primary" as const,
        onClick: () => console.log("New Draft clicked"),
        className: "!px-4 !py-2 !text-sm !font-medium !text-white !w-auto"
      }
    ]
  },

  libraryApprovals: {
    title: "Approvals",
    description: "Review and approve pending documents",
    actions: [
      {
        label: "Approve All",
        variant: "primary" as const,
        onClick: () => console.log("Approve All clicked"),
        className: "!px-4 !py-2 !text-sm !font-medium !text-white !w-auto"
      }
    ]
  },

  // Admin section configurations
  admin: {
    title: "Admin Panel",
    description: "Manage your organization settings and users",
    actions: [
      {
        label: "System Settings",
        variant: "ghost" as const,
        onClick: () => console.log("System Settings clicked"),
        className: "!px-4 !py-2 !text-sm !font-medium !border-neutral-300 !text-neutral-700 hover:!bg-neutral-50 !w-auto"
      }
    ]
  },

  adminOrgSetup: {
    title: "Organization Setup",
    description: "Configure your organization details and preferences",
    actions: [
      {
        label: "Save Changes",
        variant: "primary" as const,
        onClick: () => console.log("Save Changes clicked"),
        className: "!px-4 !py-2 !text-sm !font-medium !text-white !w-auto"
      }
    ]
  },

  adminUsers: {
    title: "User Management",
    description: "Manage users, roles, and permissions",
    actions: [
      {
        label: "Invite User",
        variant: "primary" as const,
        onClick: () => console.log("Invite User clicked"),
        className: "!px-4 !py-2 !text-sm !font-medium !text-white !w-auto"
      }
    ]
  },

  adminWorkspaces: {
    title: "Workspaces",
    description: "Create and manage team workspaces",
    actions: [
      {
        label: "New Workspace",
        variant: "primary" as const,
        onClick: () => console.log("New Workspace clicked"),
        className: "!px-4 !py-2 !text-sm !font-medium !text-white !w-auto"
      }
    ]
  },

  adminRoles: {
    title: "Roles & Permissions",
    description: "Define user roles and access permissions",
    actions: [
      {
        label: "Create Role",
        variant: "primary" as const,
        onClick: () => console.log("Create Role clicked"),
        className: "!px-4 !py-2 !text-sm !font-medium !text-white !w-auto"
      }
    ]
  },

  // Assistant section configurations
  assistant: {
    title: "AI Assistant",
    description: "Chat with your intelligent maritime assistant",
    actions: [
      {
        label: "New Chat",
        variant: "primary" as const,
        onClick: () => console.log("New Chat clicked"),
        className: "!px-4 !py-2 !text-sm !font-medium !text-white !w-auto"
      }
    ]
  },

  assistantChat: {
    title: "Chat",
    description: "Start a conversation with your AI assistant",
    actions: [
      {
        label: "Clear Chat",
        variant: "ghost" as const,
        onClick: () => console.log("Clear Chat clicked"),
        className: "!px-4 !py-2 !text-sm !font-medium !border-neutral-300 !text-neutral-700 hover:!bg-neutral-50 !w-auto"
      },
      {
        label: "New Chat",
        variant: "primary" as const,
        onClick: () => console.log("New Chat clicked"),
        className: "!px-4 !py-2 !text-sm !font-medium !text-white !w-auto"
      }
    ]
  },

  assistantHistory: {
    title: "Chat History",
    description: "View your previous conversations",
    actions: [
      {
        label: "Export History",
        variant: "ghost" as const,
        onClick: () => console.log("Export History clicked"),
        className: "!px-4 !py-2 !text-sm !font-medium !border-neutral-300 !text-neutral-700 hover:!bg-neutral-50 !w-auto"
      }
    ]
  },

  // Settings section configurations
  settings: {
    title: "Settings",
    description: "Configure your account and application preferences",
    actions: [
      {
        label: "Save Settings",
        variant: "primary" as const,
        onClick: () => console.log("Save Settings clicked"),
        className: "!px-4 !py-2 !text-sm !font-medium !text-white !w-auto"
      }
    ]
  }
};

// Helper function to get config by route
export const getHeaderConfigByRoute = (pathname: string): HeaderConfig => {
  // Remove locale prefix if present
  const cleanPath = pathname
  console.log(pathname, 'cleanPatherssss')
  // Map routes to config keys
  if (cleanPath.startsWith('/library/documents')) return headerConfigs.libraryDocuments;
  if (cleanPath.startsWith('/library/my-drafts')) return headerConfigs.libraryDrafts;
  if (cleanPath.startsWith('/library/approvals')) return headerConfigs.libraryApprovals;
  if (cleanPath.startsWith('/library')) return headerConfigs.library;

  if (cleanPath.startsWith('/admin/org-setup')) return headerConfigs.adminOrgSetup;
  if (cleanPath.startsWith('/admin/users')) return headerConfigs.adminUsers;
  if (cleanPath.startsWith('/admin/workspaces')) return headerConfigs.adminWorkspaces;
  if (cleanPath.startsWith('/admin/roles')) return headerConfigs.adminRoles;
  if (cleanPath.startsWith('/admin')) return headerConfigs.admin;

  if (cleanPath.startsWith('/assistant/chat')) return headerConfigs.assistantChat;
  if (cleanPath.startsWith('/assistant/history')) return headerConfigs.assistantHistory;
  if (cleanPath.startsWith('/assistant')) return headerConfigs.assistant;

  if (cleanPath.startsWith('/settings')) return headerConfigs.settings;

  // Default fallback
  return {
    title: "Dashboard",
    description: "Welcome to your dashboard",
    actions: []
  };
};

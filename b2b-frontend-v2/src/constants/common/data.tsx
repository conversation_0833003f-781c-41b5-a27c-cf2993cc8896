import {
  NavicaterAiIcon,
  NavicaterAiIconActive,
  LibraryIcon,
  LibraryIconActive,
  AdminPanelIcon,
  AdminPanelIconActive,
  ToggleIcon,
} from "@/assets/images/svgs/common";

export const sidebarItems = [
  {
    icon: <NavicaterAiIcon />,
    iconActive: <NavicaterAiIconActive />,
    label: "globalSidebar.navigation.navicater_ai.label",
    href: "/assistant",
    isCollapsible: true,
    isExpanded: false,
    isBottom: false,
    subItems: [
      {
        label: "globalSidebar.navigation.navicater_ai.chat",
        href: "/assistant/chat",
      },
      {
        label: "globalSidebar.navigation.navicater_ai.history",
        href: "/assistant/history",
      },
    ],
  },
  {
    icon: <LibraryIcon />,
    iconActive: <LibraryIconActive />,
    label: "globalSidebar.navigation.library.label",
    href: "/library",
    isCollapsible: true,
    isExpanded: false,
    isBottom: false,
    subItems: [
      {
        label: "globalSidebar.navigation.library.overview",
        href: "/library",
      },
      {
        label: "globalSidebar.navigation.library.documents",
        href: "/library/documents",
      },
      {
        label: "globalSidebar.navigation.library.my_drafts",
        href: "/library/my-drafts",
      },
      {
        label: "globalSidebar.navigation.library.approvals",
        href: "/library/approvals",
      },
    ],
  },
  {
    icon: <AdminPanelIcon />,
    iconActive: <AdminPanelIconActive />,
    label: "globalSidebar.navigation.admin_panel.label",
    href: "/admin",
    isCollapsible: true,
    isExpanded: false,
    isBottom: false,
    subItems: [
      {
        label: "globalSidebar.navigation.admin_panel.org_setup",
        href: "/admin/org-setup",
      },
      {
        label: "globalSidebar.navigation.admin_panel.users",
        href: "/admin/users",
      },
      {
        label: "globalSidebar.navigation.admin_panel.workspaces",
        href: "/admin/workspaces",
      },
      {
        label: "globalSidebar.navigation.admin_panel.roles",
        href: "/admin/roles",
      },
    ],
  },
  {
    icon: <ToggleIcon />,
    iconActive: <ToggleIcon />,
    label: "globalSidebar.navigation.toggle.label",
    href: "#toggle",
    isCollapsible: false,
    isExpanded: false,
    isBottom: true,
    isToggle: true,
  },
];
